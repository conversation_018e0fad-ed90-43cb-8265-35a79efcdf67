import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'

export default {
  install(app, options) {
    // Vue 3 uses app.config.globalProperties instead of Vue.prototype
    app.config.globalProperties.$getPdf = function (title = 'document', elementId = '#pdfDom') {
      return new Promise((resolve, reject) => {
        const element = document.querySelector(elementId)
        if (!element) {
          reject(new Error(`Element with selector "${elementId}" not found`))
          return
        }

        html2Canvas(element, {
          allowTaint: true,
          useCORS: true,
          scale: 2 // Better quality
        }).then(function (canvas) {
          try {
            let contentWidth = canvas.width
            let contentHeight = canvas.height
            let pageHeight = contentWidth / 592.28 * 841.89
            let leftHeight = contentHeight
            let position = 0
            let imgWidth = 595.28
            let imgHeight = 592.28 / contentWidth * contentHeight
            let pageData = canvas.toDataURL('image/jpeg', 1.0)
            let PDF = new JsPDF('', 'pt', 'a4')

            if (leftHeight < pageHeight) {
              PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
            } else {
              while (leftHeight > 0) {
                PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                leftHeight -= pageHeight
                position -= 841.89
                if (leftHeight > 0) {
                  PDF.addPage()
                }
              }
            }

            PDF.save(title + '.pdf')
            resolve(PDF)
          } catch (error) {
            reject(error)
          }
        }).catch(reject)
      })
    }

    // Also provide a composable function for use with Composition API
    app.provide('htmlToPdf', {
      generatePdf: app.config.globalProperties.$getPdf
    })
  }
}