import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'

export default {
  install(app) {
    // Vue 3 uses app.config.globalProperties instead of Vue.prototype
    app.config.globalProperties.$getPdf = function (title = 'document', elementId = '#pdfDom') {
      return new Promise((resolve, reject) => {
        const element = document.querySelector(elementId)
        if (!element) {
          reject(new Error(`Element with selector "${elementId}" not found`))
          return
        }

        // 获取元素的实际内容尺寸，排除margin
        const computedStyle = window.getComputedStyle(element)
        const marginLeft = parseFloat(computedStyle.marginLeft) || 0
        const marginTop = parseFloat(computedStyle.marginTop) || 0

        // 计算实际内容区域
        const contentWidth = element.offsetWidth
        const contentHeight = element.offsetHeight

        html2Canvas(element, {
          allowTaint: true,
          useCORS: true,
          scale: 2, // 高质量
          backgroundColor: '#ffffff',
          logging: false,
          width: contentWidth,
          height: contentHeight,
          scrollX: 0,
          scrollY: 0,
          x: marginLeft,
          y: marginTop,
          // 只截取内容区域，不包括margin
          windowWidth: contentWidth,
          windowHeight: contentHeight
        }).then(function (canvas) {
          try {
            // A4 页面尺寸 (pt): 595.28 x 841.89
            const pageWidth = 595.28
            const pageHeight = 841.89

            // 设置边距 (pt)
            const margin = 40 // 40pt 约等于 14mm
            const contentWidth = pageWidth - (margin * 2) // 可用内容宽度
            const contentHeight = pageHeight - (margin * 2) // 可用内容高度

            // 计算图片缩放比例
            const canvasWidth = canvas.width
            const canvasHeight = canvas.height
            const scaleX = contentWidth / canvasWidth
            const scaleY = contentHeight / canvasHeight
            const scale = Math.min(scaleX, scaleY) // 保持比例，取较小的缩放比

            // 计算实际图片尺寸
            const imgWidth = canvasWidth * scale
            const imgHeight = canvasHeight * scale

            // 计算居中位置
            const xOffset = margin + (contentWidth - imgWidth) / 2
            const yOffset = margin

            const PDF = new JsPDF('portrait', 'pt', 'a4')

            let remainingHeight = imgHeight
            let currentY = yOffset
            let sourceY = 0

            // 计算每页可以放置的图片高度
            const maxHeightPerPage = contentHeight

            while (remainingHeight > 0) {
              // 当前页面可以放置的高度
              const heightForThisPage = Math.min(remainingHeight, maxHeightPerPage)

              // 创建临时canvas来裁剪当前页面的内容
              const tempCanvas = document.createElement('canvas')
              const tempCtx = tempCanvas.getContext('2d')
              tempCanvas.width = canvasWidth
              tempCanvas.height = (heightForThisPage / scale)

              // 绘制当前页面的内容
              tempCtx.drawImage(
                canvas,
                0, sourceY, canvasWidth, (heightForThisPage / scale),
                0, 0, canvasWidth, (heightForThisPage / scale)
              )

              const tempPageData = tempCanvas.toDataURL('image/jpeg', 1.0)

              // 添加到PDF
              PDF.addImage(tempPageData, 'JPEG', xOffset, currentY, imgWidth, heightForThisPage)

              remainingHeight -= heightForThisPage
              sourceY += (heightForThisPage / scale)

              if (remainingHeight > 0) {
                PDF.addPage()
                currentY = yOffset // 新页面从顶部边距开始
              }
            }

            PDF.save(title + '.pdf')
            resolve(PDF)
          } catch (error) {
            reject(error)
          }
        }).catch(reject)
      })
    }

    // Also provide a composable function for use with Composition API
    app.provide('htmlToPdf', {
      generatePdf: app.config.globalProperties.$getPdf
    })
  }
}