<template>
  <div class="resume-container">
    <!-- PDF Export Button -->
    <div class="export-controls">
      <button @click="exportToPdf" class="export-btn">导出PDF</button>
    </div>

    <!-- Resume Content for PDF Export -->
    <div id="pdfDom" class="resume-content">
    <header>
      <div class="profile">
        <div class="profile-info">
          <h1 class="p-0 m-0">江南</h1>
          <p class="p-0 m-0">{{ contact.phone }} | {{ contact.email }} | {{ contact.location }}</p>
          <p class="p-0 m-0">{{ contact.wechat }} | {{ contact.experience }} | {{ contact.gender }}</p>
        </div>
        <div class="profile-image">
          <img :src="head" alt="">
        </div>
      </div>
    </header>

    <section class="section">
      <h2>教育经历</h2>
      <div class="education-item">
        <div class="edu-header">
          <span class="school sub-title">重庆第二师范学院 本科</span>
          <span class="sub-title date">2018.09</span>
        </div>
        <p class="detail">物联网工程 数学与信息工程学院</p>
      </div>
    </section>

    <section class="section">
      <h2>技术栈</h2>
      <ul class="skills-list">
        <li v-for="(skill, index) in skills" :key="index" class="flex">
          <div class="skills-list-point"></div>
          <span>{{ skill }}</span>
        </li>
      </ul>
    </section>


    <section class="section">
      <h2>技术栈</h2>
      <div v-for="(job, index) in workExperience" :key="index" class="job-item">
        <div class="job-header">
          <span class="company">{{ job.company }}</span>
          <span class="date">{{ job.period }}</span>
        </div>
        <p class="position">{{ job.position }}</p>
      </div>
    </section>
    </div> <!-- End of pdfDom -->
  </div>
</template>


<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import head from './assets/4.png'

// Get the current instance to access global properties
const instance = getCurrentInstance()

// Export to PDF function
const exportToPdf = async () => {
  try {
    if (instance?.appContext.app.config.globalProperties.$getPdf) {
      await instance.appContext.app.config.globalProperties.$getPdf('江南-简历', '#pdfDom')
      console.log('PDF exported successfully')
    } else {
      console.error('PDF export function not available')
    }
  } catch (error) {
    console.error('Error exporting PDF:', error)
  }
}

const contact = {
  phone: '13527354870',
  email: '<EMAIL>',
  location: '重庆',
  wechat: '微信同号',
  experience: '二、三维前端开发',
  gender: '男'
}

const skills = [
  '精通 Javascript、Ts、ES6、CSS3(less/scss)、HTML5，有多个前端项目开发经验',
  '精通 Threejs、Echarts 等三维数字学科可视化技术，精通对大屏、跨屏幕的前端自适应分辨率开发，熟悉 Shader、webGL，参与翻译Three.js中文文档',
  '精通 Vue2/3、Vite、Webpack、VSCode 等前端常用工具，技术与脚手架',
  '精通 Class 类与面向对象的前端开发模式，掌握设计式编程与面向对象编程等开发模式',
  '熟练掌握 async + await、Promise、回调等同步与异步请求思想与处理能力，保证代码可读性',
  '熟练掌握 Git、CI/CD、Linker、镜像、apipost、postman、画湖、层刀等工具，有协同合作经验',
  '熟练掌握 Node.js、Nginx、MySQL，独立部署过服务器，对后端有一定了解',
  '熟悉用户体验、交互操作流程、及用户需求',
  'PS专精'
]

const workExperience = [
  {
    company: '重庆双高实业有限公司',
    position: '数据业务线 前端开发组长',
    period: '2024.06 - 至今'
  },
  {
    company: '重庆驰图科技有限公司',
    position: '数字孪生二、三维 前端开发',
    period: '2022.03 - 2024.05'
  }
]
</script>

<style scoped lang="less">
* {
  font-family: 'OPPO Sans 4.0';
}

.resume-container {
  width: 100%;
  margin: 0 auto;
  padding: 20px 0px;
}

header {
  margin-bottom: 30px;
}

.profile {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-info h1 {
  display: flex;
  justify-self: center;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 10px;
  margin-left: 10px;
}

.profile-info p {
  display: flex;
  justify-self: center;
  font-weight: 500;
  font-size: 12px;
}

.section {
  margin-bottom: 10px;
}

h2 {
  font-size: 14px;
  border-bottom: 2px solid #6c6c6c;
  padding-bottom: 2px;
  margin-bottom: 8px;
  margin-top: 0;
}

.education-item,
.job-item {
  margin-bottom: 15px;
}

.edu-header,
.job-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
}

.skills-list {
  padding: 0;
  margin: 0 0 0 4px;
}

.skills-list li {
  margin-bottom: 4px;
  position: relative;
  padding-left: 12px;
  list-style: none;
  font-size: 12px;
}

.skills-list-point {
  position: absolute;
  left: 0;
  top: 8px;
  width: 4px;
  height: 4px;
  background: black;
  border-radius: 4px;
}

.profile-image {
  position: absolute;
  width: 72px;
  right: 20px;
  top: 40px;
}

.profile-image img {
  width: 100%;
}

.sub-title {
  font-size: 12px;
}

.date {
  font-weight: 400;
  font-size: 12px;
}


.detail {
  font-size: 12px;
  margin: 4px 0;
}

.company {
  font-size: 12px;
}

.position {
  font-size: 12px;
}

.export-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.export-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}

.export-btn:hover {
  background: #0056b3;
}

.export-btn:active {
  transform: translateY(1px);
}

@media print {
  .export-controls {
    display: none;
  }
}
</style>