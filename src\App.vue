<template>
  <div class="resume-container">
    <!-- PDF Export Button -->
    <div class="export-controls">
      <button @click="exportToPdf" class="export-btn">导出PDF</button>
    </div>

    <!-- Resume Content for PDF Export -->
    <div id="pdfDom" class="resume-content">
      <header>
        <div class="profile">
          <div class="profile-info">
            <h1 class="p-0 m-0">江南</h1>
            <p class="p-0 m-0">{{ contact.phone }} | {{ contact.email }} | {{ contact.location }}</p>
            <p class="p-0 m-0">{{ contact.wechat }} | {{ contact.experience }} | {{ contact.gender }}</p>
          </div>
          <div class="profile-image">
            <img :src="head" alt="">
          </div>
        </div>
      </header>

      <section class="section">
        <h2>教育经历</h2>
        <div class="education-item">
          <div class="edu-header">
            <span class="school sub-title">重庆第二师范学院 本科</span>
            <span class="sub-title date">2018.09</span>
          </div>
          <p class="detail">物联网工程 数学与信息工程学院</p>
        </div>
      </section>

      <section class="section">
        <h2>技术栈</h2>
        <ul class="skills-list">
          <li v-for="(skill, index) in skills" :key="index" class="flex">
            <div class="skills-list-point"></div>
            <span>{{ skill }}</span>
          </li>
        </ul>
      </section>


      <section class="section">
        <h2>技术栈</h2>
        <div v-for="(job, index) in workExperience" :key="index" class="job-item">
          <div class="job-header">
            <span class="company">{{ job.company }}</span>
            <span class="date">{{ job.period }}</span>
          </div>
          <p class="position">{{ job.position }}</p>
        </div>
      </section>
    </div> <!-- End of pdfDom -->
  </div>
</template>


<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import head from './assets/4.png'

// Get the current instance to access global properties
const instance = getCurrentInstance()

// Export to PDF function
const exportToPdf = async () => {
  try {
    const pdfElement = document.querySelector('#pdfDom')
    if (!pdfElement) {
      console.error('PDF element not found')
      return
    }

    // 临时添加PDF导出样式
    pdfElement.classList.add('pdf-export-mode')

    if (instance?.appContext.app.config.globalProperties.$getPdf) {
      await instance.appContext.app.config.globalProperties.$getPdf('江南-简历', '#pdfDom')
      console.log('PDF exported successfully')
    } else {
      console.error('PDF export function not available')
    }

    // 移除临时样式
    pdfElement.classList.remove('pdf-export-mode')
  } catch (error) {
    console.error('Error exporting PDF:', error)
    // 确保在出错时也移除临时样式
    const pdfElement = document.querySelector('#pdfDom')
    if (pdfElement) {
      pdfElement.classList.remove('pdf-export-mode')
    }
  }
}

const contact = {
  phone: '13527354870',
  email: '<EMAIL>',
  location: '重庆',
  wechat: '微信同号',
  experience: '二、三维前端开发',
  gender: '男'
}

const skills = [
  '精通 Javascript、Ts、ES6、CSS3(less/scss)、HTML5，有多个前端项目开发经验；',
  '精通 Threejs、Echarts 等三维数字孪生可视化技术，精通对大屏、异形屏的前端自适应分辨率开发，熟悉 shader、webGL。参与翻译Three.js中文文档；',
  '精通 Vue2/3、Vite、Webpack、VSCode 等前端常用工具、技术与脚手架；',
  '精通 Class 类与面向对象的前端开发模式，掌握函数式编程与面向对象编程等开发模式；',
  '熟练掌握 微信小程序，开发过包含微信登录、微信支付等功能的应用，并独立备案、提交审查、部署上线；',
  '熟练掌握 async + await、Promise、回调等同步与异步请求思想与处理能力，保证代码可读性；',
  '熟练掌握 Git、CI/CD、Linker、禅道、apipost、postman、蓝湖、磨刀等工具，有协同合作经验；',
  '熟练掌握 Node.js、Nginx、MySQL，独立部署过服务器，对后端有一定了解；',
  '熟悉用户体验、交互操作流程、及用户需求；',
  'PS专精。'
]

const workExperience = [
  {
    company: '重庆双高实业有限公司',
    position: '数据业务线 前端开发组长',
    period: '2024.06 - 至今',
    detail: [{
      title: '角色职责与任务',
      detail: [
        '参与原型和 UI 设计评审',
        '讨论 UI 展示、数据来源等系统相关内容及存在的问题。包括地图交互与功能、缺乏设计感、字形、热力范围设计不佳、和其它系统的集成难度等。及时提出原型和 UI 评审阶段存在的问题并给出建议。',
        '领导并协调前端开发团队',
        '根据团队成员的技能和项目需求，合理分配工作任务，提高团队效率。定期进行代码审核，确保代码质量和一致性。为团队成员提供技术支持和培训，提升整体技术水平。']
    },{
      title: '2、能源大数据管理平台(ECharts/ Three.js)',
      detail: [

      可视化组件开发与优化
主导 ECharts 组件库二次封装，基于业务场景抽象通用配置模板，实现 8 种标准化图表组件，支持动态数据渲染、主题切换及响应式布局，开发效率提升35 % 。
      通过按需加载、动态注册策略优化组件体积，单图表资源加载耗时降低至 500ms 以内。
      三维地理信息可视化系统
独立完成 Three.js 三维地图引擎开发，增加辉光、轮廓、RGB、抗锯齿等滤镜和效果，实现省级行政区地形渲染、动态热力图更新及3D模型交互功能，FPS 稳定在60帧以上。实现地理位置信息 Geojson 生成模型，并实现开屏动画以及场景切换、运镜等效果。
      特殊效果制作
独立完成动态水平面的片元着色器制作；独立完成三维旋转轮盘；独立实现适配 Echarts 地图的自定义内阴影。
      2、关键指标看板
多端适配
支持 PC 端、移动端（H5）以及 IE11 浏览器，采用 Vue 2.7 版本进行开发，使用 Echarts 图表，实施按需加载策略，控制每个页面的图表数量以保证 IE 浏览器和移动端的使用体验，所有组件均采用自适应设计，确保移动端用户有充足的安全区。兼顾键鼠与移动端的操作习惯，提升用户体验。
      权限控制
权限细分与后端系统协同，实施四级目录的权限控制，允许在后台灵活配置每个四级目录的权限指标。
      严格执行上线流程
按照 提交测试、修复问题、进行回归测试、审核通过、正式上线、上线后测试、发布上线 的流程上线，确保项目成功上架至国家电网的内网环境中。
    ]
  },
  {
    company: '重庆驰图科技有限公司',
    position: '数字孪生二、三维 前端开发',
    period: '2022.03 - 2024.05'
  }
]
</script>

<style scoped lang="less">
* {
  font-family: 'OPPO Sans 4.0';
}

.resume-container {
  width: 100%;
  margin: 0 auto;
}

.resume-content {
  border: 2px solid #333;
  padding: 30px;
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

header {
  margin-bottom: 30px;
}

.profile {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-info h1 {
  display: flex;
  justify-self: center;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 10px;
  margin-left: 10px;
}

.profile-info p {
  display: flex;
  justify-self: center;
  font-weight: 500;
  font-size: 12px;
}

.section {
  margin-bottom: 10px;
}

h2 {
  font-size: 14px;
  border-bottom: 2px solid #6c6c6c;
  padding-bottom: 2px;
  margin-bottom: 8px;
  margin-top: 0;
}

.education-item,
.job-item {
  margin-bottom: 15px;
}

.edu-header,
.job-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
}

.skills-list {
  padding: 0;
  margin: 0 0 0 4px;
}

.skills-list li {
  margin-bottom: 4px;
  position: relative;
  padding-left: 12px;
  list-style: none;
  font-size: 12px;
}

.skills-list-point {
  position: absolute;
  left: 0;
  top: 8px;
  width: 4px;
  height: 4px;
  background: black;
  border-radius: 4px;
}

.profile-image {
  position: absolute;
  width: 72px;
  right: 20px;
  top: 40px;
}

.profile-image img {
  width: 100%;
}

.sub-title {
  font-size: 12px;
}

.date {
  font-weight: 400;
  font-size: 12px;
}


.detail {
  font-size: 12px;
  margin: 4px 0;
}

.company {
  font-size: 12px;
}

.position {
  font-size: 12px;
}

.export-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.export-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}


.export-btn:active {
  transform: translateY(1px);
}

/* PDF导出专用样式 */
.pdf-export-mode {
  border: 3px solid #000 !important;
  padding: 40px !important;
  background: white !important;
  box-shadow: none !important;
  margin: 0 !important;
}

.pdf-export-mode header {
  padding-bottom: 15px;
}

.pdf-export-mode .section {
  padding-left: 15px;
}

.pdf-export-mode h2 {
  border-bottom: 2px solid #333;
  color: #333;
  font-weight: bold;
}

/* PDF导出专用样式 */
.pdf-export-mode {
  padding: 40px !important;
  border: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  background: white !important;
  min-height: auto !important;
}

.pdf-export-mode .profile-image {
  position: absolute;
  width: 72px;
  right: 40px !important;
  /* 调整右边距以适应PDF边距 */
  top: 40px !important;
}

@media print {
  .export-controls {
    display: none;
  }

  .resume-content {
    border: 2px solid #000 !important;
    box-shadow: none !important;
    padding: 40px !important;
  }
}
</style>